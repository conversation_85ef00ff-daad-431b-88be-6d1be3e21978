<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Create tables if they don't exist
try {
    $sql = file_get_contents(__DIR__ . '/sql/events_tables.sql');
    $conn->exec($sql);
} catch (PDOException $e) {
    error_log("Error creating events tables: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_event':
                try {
                    $stmt = $conn->prepare("
                        INSERT INTO events (title, description, event_date, location,
                                          max_attendees, created_by, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");

                    $stmt->execute([
                        $_POST['title'],
                        $_POST['description'],
                        $_POST['start_datetime'], // Use start_datetime as event_date
                        $_POST['location'],
                        !empty($_POST['capacity']) ? (int)$_POST['capacity'] : null,
                        $_SESSION['admin_id'],
                        ($_POST['status'] === 'published') ? 1 : 0 // Convert status to is_active
                    ]);
                    
                    $message = "Event created successfully!";
                } catch (PDOException $e) {
                    $error = "Error creating event: " . $e->getMessage();
                }
                break;
                
            case 'update_event':
                try {
                    $stmt = $conn->prepare("
                        UPDATE events SET title = ?, description = ?, event_date = ?,
                               location = ?, max_attendees = ?, is_active = ?
                        WHERE id = ?
                    ");

                    $stmt->execute([
                        $_POST['title'],
                        $_POST['description'],
                        $_POST['start_datetime'], // Use start_datetime as event_date
                        $_POST['location'],
                        !empty($_POST['capacity']) ? (int)$_POST['capacity'] : null,
                        ($_POST['status'] === 'published') ? 1 : 0, // Convert status to is_active
                        (int)$_POST['event_id']
                    ]);
                    
                    $message = "Event updated successfully!";
                } catch (PDOException $e) {
                    $error = "Error updating event: " . $e->getMessage();
                }
                break;
                
            case 'delete_event':
                try {
                    $stmt = $conn->prepare("DELETE FROM events WHERE id = ?");
                    $stmt->execute([(int)$_POST['event_id']]);
                    $message = "Event deleted successfully!";
                } catch (PDOException $e) {
                    $error = "Error deleting event: " . $e->getMessage();
                }
                break;
        }
    }
}

// Get events with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Build search and filter conditions
$where_conditions = [];
$params = [];

if (!empty($_GET['search'])) {
    $where_conditions[] = "(title LIKE ? OR description LIKE ? OR location LIKE ?)";
    $search_term = '%' . $_GET['search'] . '%';
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($_GET['active'])) {
    $where_conditions[] = "is_active = ?";
    $params[] = (int)$_GET['active'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count for pagination
$count_sql = "SELECT COUNT(*) FROM events e $where_clause";
$count_stmt = $conn->prepare($count_sql);
$count_stmt->execute($params);
$total_events = $count_stmt->fetchColumn();
$total_pages = ceil($total_events / $limit);

// Get events
$sql = "
    SELECT e.*,
           (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'attending') as attending_count
    FROM events e
    $where_clause
    ORDER BY e.event_date DESC
    LIMIT $limit OFFSET $offset
";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Categories not used in current table structure
$categories = [];

// Page title and header info
$page_title = "Events Management";
$page_header = "Events Management";
$page_description = "Create, manage, and track church events and activities.";

// Include header
include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#eventModal">
            <i class="bi bi-plus-circle"></i> Create Event
        </button>
        <a href="event_categories.php" class="btn btn-outline-secondary ms-2">
            <i class="bi bi-tags"></i> Manage Categories
        </a>
        <a href="event_reports.php" class="btn btn-outline-info ms-2">
            <i class="bi bi-graph-up"></i> Reports
        </a>
    </div>
    <div>
        <a href="../events.php" class="btn btn-outline-primary" target="_blank">
            <i class="bi bi-calendar-event"></i> View Public Calendar
        </a>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Events</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" 
                       placeholder="Search by title, description, or location">
            </div>
            <div class="col-md-3">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category" name="category">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?= $category['id'] ?>" 
                                <?= ($_GET['category'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="draft" <?= ($_GET['status'] ?? '') == 'draft' ? 'selected' : '' ?>>Draft</option>
                    <option value="published" <?= ($_GET['status'] ?? '') == 'published' ? 'selected' : '' ?>>Published</option>
                    <option value="cancelled" <?= ($_GET['status'] ?? '') == 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                    <option value="completed" <?= ($_GET['status'] ?? '') == 'completed' ? 'selected' : '' ?>>Completed</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="bi bi-search"></i> Filter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Events Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Events (<?= $total_events ?> total)</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($events)): ?>
            <div class="text-center py-5">
                <i class="bi bi-calendar-x display-1 text-muted"></i>
                <h4 class="mt-3">No Events Found</h4>
                <p class="text-muted">Create your first event to get started.</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#eventModal">
                    <i class="bi bi-plus-circle"></i> Create Event
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Event</th>
                            <th>Date & Time</th>
                            <th>Location</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>RSVPs</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($events as $event): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?= htmlspecialchars($event['title']) ?></strong>
                                        <?php if ($event['description']): ?>
                                            <br><small class="text-muted">
                                                <?= htmlspecialchars(substr($event['description'], 0, 100)) ?>
                                                <?= strlen($event['description']) > 100 ? '...' : '' ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= date('M j, Y', strtotime($event['event_date'])) ?></strong>
                                        <br><small class="text-muted">
                                            <?= date('g:i A', strtotime($event['event_date'])) ?>
                                        </small>
                                    </div>
                                </td>
                                <td><?= htmlspecialchars($event['location'] ?? 'TBD') ?></td>
                                <td>
                                    <span class="text-muted">Uncategorized</span>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $event['is_active'] ? 'success' : 'secondary' ?>">
                                        <?= $event['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-primary">
                                        <?= $event['attending_count'] ?>
                                        <?php if ($event['max_attendees']): ?>
                                            / <?= $event['max_attendees'] ?>
                                        <?php endif; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary edit-event-btn"
                                                data-event='<?= json_encode($event) ?>'>
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <a href="../event_detail.php?id=<?= $event['id'] ?>" 
                                           class="btn btn-outline-info" target="_blank">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger delete-event-btn"
                                                data-id="<?= $event['id'] ?>" data-title="<?= htmlspecialchars($event['title']) ?>">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav aria-label="Events pagination">
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page - 1 ?><?= !empty($_GET['search']) ? '&search=' . urlencode($_GET['search']) : '' ?><?= !empty($_GET['category']) ? '&category=' . $_GET['category'] : '' ?><?= !empty($_GET['status']) ? '&status=' . $_GET['status'] : '' ?>">
                                Previous
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?><?= !empty($_GET['search']) ? '&search=' . urlencode($_GET['search']) : '' ?><?= !empty($_GET['category']) ? '&category=' . $_GET['category'] : '' ?><?= !empty($_GET['status']) ? '&status=' . $_GET['status'] : '' ?>">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page + 1 ?><?= !empty($_GET['search']) ? '&search=' . urlencode($_GET['search']) : '' ?><?= !empty($_GET['category']) ? '&category=' . $_GET['category'] : '' ?><?= !empty($_GET['status']) ? '&status=' . $_GET['status'] : '' ?>">
                                Next
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<!-- Event Modal -->
<div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventModalLabel">Create Event</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="eventForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" id="eventAction" value="create_event">
                    <input type="hidden" name="event_id" id="eventId">

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="eventTitle" class="form-label">Event Title *</label>
                            <input type="text" class="form-control" id="eventTitle" name="title" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="eventCategory" class="form-label">Category</label>
                            <select class="form-select" id="eventCategory" name="category_id">
                                <option value="">Select Category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="eventDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="eventDescription" name="description" rows="4"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="eventStartDate" class="form-label">Start Date & Time *</label>
                            <input type="datetime-local" class="form-control" id="eventStartDate" name="start_datetime" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="eventEndDate" class="form-label">End Date & Time *</label>
                            <input type="datetime-local" class="form-control" id="eventEndDate" name="end_datetime" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="eventLocation" class="form-label">Location</label>
                            <input type="text" class="form-control" id="eventLocation" name="location" placeholder="Event location or address">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="eventCapacity" class="form-label">Capacity</label>
                            <input type="number" class="form-control" id="eventCapacity" name="capacity" min="1" placeholder="Optional">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="eventRequirements" class="form-label">Requirements/Notes</label>
                        <textarea class="form-control" id="eventRequirements" name="requirements" rows="2" placeholder="Any special requirements or notes for attendees"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="eventStatus" class="form-label">Status</label>
                            <select class="form-select" id="eventStatus" name="status">
                                <option value="draft">Draft</option>
                                <option value="published">Published</option>
                                <option value="cancelled">Cancelled</option>
                                <option value="completed">Completed</option>
                            </select>
                        </div>
                        <div class="col-md-8 mb-3">
                            <label for="eventRegDeadline" class="form-label">Registration Deadline</label>
                            <input type="datetime-local" class="form-control" id="eventRegDeadline" name="registration_deadline">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="eventPublic" name="is_public" checked>
                                <label class="form-check-label" for="eventPublic">
                                    Public Event
                                </label>
                                <small class="form-text text-muted d-block">Visible on public calendar</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="eventRegistration" name="registration_required">
                                <label class="form-check-label" for="eventRegistration">
                                    Registration Required
                                </label>
                                <small class="form-text text-muted d-block">Require RSVP to attend</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <span id="submitButtonText">Create Event</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the event "<span id="deleteEventTitle"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete_event">
                    <input type="hidden" name="event_id" id="deleteEventId">
                    <button type="submit" class="btn btn-danger">Delete Event</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Edit event functionality
    document.querySelectorAll('.edit-event-btn').forEach(button => {
        button.addEventListener('click', function() {
            const eventData = JSON.parse(this.getAttribute('data-event'));

            // Update modal title and form action
            document.getElementById('eventModalLabel').textContent = 'Edit Event';
            document.getElementById('eventAction').value = 'update_event';
            document.getElementById('eventId').value = eventData.id;
            document.getElementById('submitButtonText').textContent = 'Update Event';

            // Populate form fields
            document.getElementById('eventTitle').value = eventData.title || '';
            document.getElementById('eventDescription').value = eventData.description || '';
            document.getElementById('eventStartDate').value = eventData.start_datetime ? eventData.start_datetime.replace(' ', 'T') : '';
            document.getElementById('eventEndDate').value = eventData.end_datetime ? eventData.end_datetime.replace(' ', 'T') : '';
            document.getElementById('eventLocation').value = eventData.location || '';
            document.getElementById('eventCapacity').value = eventData.capacity || '';
            document.getElementById('eventCategory').value = eventData.category_id || '';
            document.getElementById('eventStatus').value = eventData.status || 'draft';
            document.getElementById('eventRequirements').value = eventData.requirements || '';
            document.getElementById('eventRegDeadline').value = eventData.registration_deadline ? eventData.registration_deadline.replace(' ', 'T') : '';
            document.getElementById('eventPublic').checked = eventData.is_public == '1';
            document.getElementById('eventRegistration').checked = eventData.registration_required == '1';

            // Show modal
            new bootstrap.Modal(document.getElementById('eventModal')).show();
        });
    });

    // Reset modal when creating new event
    document.querySelector('[data-bs-target="#eventModal"]').addEventListener('click', function() {
        // Reset modal title and form action
        document.getElementById('eventModalLabel').textContent = 'Create Event';
        document.getElementById('eventAction').value = 'create_event';
        document.getElementById('eventId').value = '';
        document.getElementById('submitButtonText').textContent = 'Create Event';

        // Reset form
        document.getElementById('eventForm').reset();
        document.getElementById('eventPublic').checked = true;
    });

    // Delete event functionality
    document.querySelectorAll('.delete-event-btn').forEach(button => {
        button.addEventListener('click', function() {
            const eventId = this.getAttribute('data-id');
            const eventTitle = this.getAttribute('data-title');

            document.getElementById('deleteEventId').value = eventId;
            document.getElementById('deleteEventTitle').textContent = eventTitle;

            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });

    // Form validation
    document.getElementById('eventForm').addEventListener('submit', function(e) {
        const startDate = new Date(document.getElementById('eventStartDate').value);
        const endDate = new Date(document.getElementById('eventEndDate').value);

        if (endDate <= startDate) {
            e.preventDefault();
            alert('End date must be after start date.');
            return false;
        }

        const regDeadline = document.getElementById('eventRegDeadline').value;
        if (regDeadline) {
            const deadlineDate = new Date(regDeadline);
            if (deadlineDate >= startDate) {
                e.preventDefault();
                alert('Registration deadline must be before the event start date.');
                return false;
            }
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
