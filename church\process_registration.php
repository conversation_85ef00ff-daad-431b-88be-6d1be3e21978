<?php
// Simple registration processing for original church management system
require_once 'config.php';

// Initialize error logging
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Function to handle file upload
function handleFileUpload($file) {
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return null;
    }

    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    $max_size = 5 * 1024 * 1024; // 5MB

    if (!in_array($file['type'], $allowed_types)) {
        return null;
    }

    if ($file['size'] > $max_size) {
        return null;
    }

    $upload_dir = 'uploads/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $new_filename = uniqid() . '.' . $file_extension;
    $upload_path = $upload_dir . $new_filename;

    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return $upload_path;
    }

    return null;
}

// Process registration form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate required fields
        $required_fields = ['full_name', 'email', 'birth_date', 'password', 'confirm_password'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Required field '$field' is missing.");
            }
        }

        // Validate email format
        if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email format.');
        }

        // Validate password match
        if ($_POST['password'] !== $_POST['confirm_password']) {
            throw new Exception('Passwords do not match.');
        }

        // Get form data
        $full_name = trim($_POST['full_name']);
        $occupation = trim($_POST['occupation'] ?? '');
        $email = trim($_POST['email']);
        $phone_number = trim($_POST['phone_number'] ?? '');
        $home_address = trim($_POST['home_address'] ?? '');
        $birth_date = $_POST['birth_date'];
        $message = trim($_POST['message'] ?? '');
        $password = $_POST['password'];

        // Hash the password
        $password_hash = password_hash($password, PASSWORD_DEFAULT);

        // Split full name into first and last name
        $name_parts = explode(' ', $full_name, 2);
        $first_name = $name_parts[0];
        $last_name = isset($name_parts[1]) ? $name_parts[1] : '';

        // Handle file upload
        $image_path = null;
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $image_path = handleFileUpload($_FILES['profile_image']);
        }

        // Insert into database including password_hash (if column exists)
        $stmt = $pdo->prepare("
            INSERT INTO members
            (full_name, first_name, last_name, occupation, image_path, email,
             birth_date, home_address, phone_number, message, password_hash, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
        ");

        $stmt->execute([
            $full_name,
            $first_name,
            $last_name,
            $occupation,
            $image_path,
            $email,
            $birth_date,
            $home_address,
            $phone_number,
            $message,
            $password_hash
        ]);

        // Send welcome email (optional)
        try {
            // Basic email sending - you can enhance this
            $subject = "Welcome to Our Church Family!";
            $body = "Dear $first_name,\n\nWelcome to our church family! We're excited to have you join us.\n\nBlessings,\nChurch Management Team";
            $headers = "From: noreply@" . $_SERVER['HTTP_HOST'];

            $emailSent = mail($email, $subject, $body, $headers);
        } catch (Exception $e) {
            $emailSent = false;
        }

        // Redirect with success message
        header('Location: register.php?success=1&email=' . ($emailSent ? '1' : '0'));
        exit;

    } catch (PDOException $e) {
        if ($e->getCode() == 23000) { // Duplicate entry error
            header('Location: register.php?error=duplicate_email');
        } else {
            error_log('Database error: ' . $e->getMessage());
            header('Location: register.php?error=db_error');
        }
        exit;
    } catch (Exception $e) {
        error_log('Registration error: ' . $e->getMessage());
        header('Location: register.php?error=general');
        exit;
    }
} else {
    // Not a POST request
    header('Location: register.php');
    exit;
}
?>
            throw new Exception('Invalid email format.');
        }

        // Validate password match
        if ($_POST['password'] !== $_POST['confirm_password']) {
            throw new Exception('Passwords do not match.');
        }

        // Validate password strength
        if (!$security->validateInput($_POST['password'], 'password')) {
            throw new Exception('Password does not meet security requirements. Please use a stronger password.');
        }

        // Sanitize input data
        $full_name = $security->sanitizeInput($_POST['full_name'], 'text');
        $occupation = $security->sanitizeInput($_POST['occupation'] ?? '', 'text');
        $email = $security->sanitizeInput($_POST['email'], 'email');
        $phone_number = $security->sanitizeInput($_POST['phone_number'] ?? '', 'text');
        $home_address = $security->sanitizeInput($_POST['home_address'] ?? '', 'text');
        $birth_date = $security->sanitizeInput($_POST['birth_date'], 'text');
        $message = $security->sanitizeInput($_POST['message'] ?? '', 'text');
        $password = $_POST['password']; // Don't sanitize password, just hash it
        
        // Split full name into first and last name
        $name_parts = explode(' ', $full_name, 2);
        $first_name = $name_parts[0];
        $last_name = isset($name_parts[1]) ? $name_parts[1] : '';

        // Hash password
        $hashedPassword = $security->hashPassword($password);
        
        // Handle file upload
        $image_path = null;
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] !== UPLOAD_ERR_NO_FILE) {
            try {
                validateFileUpload($_FILES['profile_image']);
                
                $upload_dir = __DIR__ . '/uploads/profiles/';
                if (!is_dir($upload_dir)) {
                    if (!mkdir($upload_dir, 0755, true)) {
                        throw new Exception('Failed to create upload directory.');
                    }
                }
                
                if (!is_writable($upload_dir)) {
                    error_log('Upload directory is not writable: ' . $upload_dir);
                    throw new Exception('Server configuration error. Please contact the administrator.');
                }
                
                $file_extension = strtolower(pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION));
                $file_name = uniqid() . '.' . $file_extension;
                $target_path = $upload_dir . $file_name;
                
                if (!move_uploaded_file($_FILES['profile_image']['tmp_name'], $target_path)) {
                    throw new Exception('Failed to move uploaded file.');
                }
                
                $image_path = 'uploads/profiles/' . $file_name;
            } catch (Exception $e) {
                logError('File upload error: ' . $e->getMessage(), [
                    'file' => $_FILES['profile_image']['name'],
                    'type' => $_FILES['profile_image']['type'],
                    'size' => $_FILES['profile_image']['size']
                ]);
                header('Location: register.php?error=file_upload&message=' . urlencode($e->getMessage()));
                exit;
            }
        }
        
        // Database operation
        try {
            $stmt = $pdo->prepare("INSERT INTO members (full_name, occupation, image_path, email, phone_number, home_address, birth_date, message, first_name, last_name, password, temp_password, must_change_password, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

            $stmt->execute([
                $full_name,
                $occupation,
                $image_path,
                $email,
                $phone_number,
                $home_address,
                $birth_date,
                $message,
                $first_name,
                $last_name,
                $hashedPassword,
                0, // temp_password = false (user created their own password)
                0, // must_change_password = false
                'active', // status
                0  // email_verified = false (can be verified later)
            ]);

            $userId = $pdo->lastInsertId();

            // Log user registration activity
            $userAuth->logUserActivity($userId, 'registration', 'User registered new account with password');

            // Send welcome email
            $memberData = [
                'full_name' => $full_name,
                'first_name' => $first_name,
                'last_name' => $last_name,
                'email' => $email,
                'phone_number' => $phone_number,
                'home_address' => $home_address,
                'occupation' => $occupation,
                'image_path' => $image_path
            ];

            try {
                $emailSent = sendWelcomeEmail($memberData);
                if (!$emailSent) {
                    logError('Failed to send welcome email', ['email' => $email]);
                }
                
                header('Location: register.php?success=1&email=' . ($emailSent ? '1' : '0'));
                exit;
            } catch (Exception $e) {
                logError('Email sending error: ' . $e->getMessage(), ['email' => $email]);
                header('Location: register.php?success=1&email=0');
                exit;
            }
        } catch (PDOException $e) {
            logError('Database error: ' . $e->getMessage(), [
                'code' => $e->getCode(),
                'email' => $email
            ]);

            if ($e->getCode() == 23000) { // Duplicate entry error
                header('Location: register.php?error=duplicate_email');
            } else {
                header('Location: register.php?error=db_error');
            }
            exit;
        }
    } catch (Exception $e) {
        logError('General error: ' . $e->getMessage(), $_POST);
        header('Location: register.php?error=general&message=' . urlencode($e->getMessage()));
        exit;
    }
} else {
    header('Location: register.php');
    exit;
}