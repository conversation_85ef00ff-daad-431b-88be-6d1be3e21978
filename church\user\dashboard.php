<?php
/**
 * User Dashboard
 * 
 * Main dashboard for authenticated users
 */

// Include configuration first (which sets up session configuration)
require_once '../config.php';

// Start session after configuration is loaded
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Check if user must change password (redirect to change password page)
if ($userData['must_change_password'] && !isset($_GET['password_changed'])) {
    header("Location: change_password.php");
    exit();
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}

// Get user preferences
$preferences = $userAuth->getUserPreferences($userId);

// Get today's birthdays
$todaysBirthdays = [];
$stmt = $pdo->prepare("
    SELECT id, full_name, first_name, last_name, email, image_path, birth_date
    FROM members
    WHERE status = 'active'
    AND id != ?
    AND MONTH(birth_date) = MONTH(CURDATE())
    AND DAY(birth_date) = DAY(CURDATE())
    ORDER BY full_name
");
$stmt->execute([$userId]);
$todaysBirthdays = $stmt->fetchAll();

// Get upcoming birthdays (next 7 days)
$upcomingBirthdays = [];
$stmt = $pdo->prepare("
    SELECT id, full_name, first_name, last_name, email, image_path, birth_date,
           DATEDIFF(
               DATE_ADD(
                   MAKEDATE(YEAR(CURDATE()), 1),
                   INTERVAL DAYOFYEAR(birth_date) - 1 DAY
               ),
               CURDATE()
           ) AS days_until
    FROM members
    WHERE status = 'active'
    AND id != ?
    AND (
        (MONTH(birth_date) = MONTH(CURDATE()) AND DAY(birth_date) > DAY(CURDATE()))
        OR (MONTH(birth_date) = MONTH(DATE_ADD(CURDATE(), INTERVAL 7 DAY)) AND DAY(birth_date) <= DAY(DATE_ADD(CURDATE(), INTERVAL 7 DAY)))
    )
    ORDER BY
        MONTH(birth_date), DAY(birth_date)
    LIMIT 5
");
$stmt->execute([$userId]);
$upcomingBirthdays = $stmt->fetchAll();

// Get recent birthday messages sent by user
$recentBirthdayMessages = [];
$stmt = $pdo->prepare("
    SELECT ubm.*, m.full_name as recipient_name, bt.name as template_name
    FROM user_birthday_messages ubm
    LEFT JOIN members m ON ubm.recipient_id = m.id
    LEFT JOIN birthday_templates bt ON ubm.template_id = bt.id
    WHERE ubm.sender_id = ?
    ORDER BY ubm.created_at DESC
    LIMIT 5
");
$stmt->execute([$userId]);
$recentBirthdayMessages = $stmt->fetchAll();

// Get recent activity from user activity logs
$recentActivity = [];
try {
    $stmt = $pdo->prepare("
        SELECT activity_type, activity_description, created_at
        FROM user_activity_log
        WHERE member_id = ?
        ORDER BY created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$userId]);
    $recentActivity = $stmt->fetchAll();
} catch (PDOException $e) {
    // Handle case where activity_description column doesn't exist
    try {
        $stmt = $pdo->prepare("
            SELECT activity_type, activity_type as activity_description, created_at
            FROM user_activity_log
            WHERE member_id = ?
            ORDER BY created_at DESC
            LIMIT 5
        ");
        $stmt->execute([$userId]);
        $recentActivity = $stmt->fetchAll();
    } catch (PDOException $e2) {
        // If table doesn't exist or has different structure, just use empty array
        $recentActivity = [];
    }
}

// Get upcoming events (placeholder for now)
$upcomingEvents = [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: white !important;
        }
        
        .dashboard-container {
            margin-top: 2rem;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }
        
        .welcome-card h2 {
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .welcome-card p {
            opacity: 0.9;
            margin: 0;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }
        
        .dashboard-card h5 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .quick-action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0.25rem;
        }
        
        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .stat-card {
            text-align: center;
            padding: 1.5rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6c757d;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid rgba(255,255,255,0.3);
        }
        
        .alert-success {
            border-radius: 12px;
            border: none;
            background-color: #d4edda;
            color: #155724;
        }
        
        .btn-outline-light {
            border-color: rgba(255,255,255,0.5);
            color: rgba(255,255,255,0.9);
        }
        
        .btn-outline-light:hover {
            background-color: rgba(255,255,255,0.1);
            border-color: white;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="birthday_templates.php">
                            <i class="bi bi-gift"></i> Birthdays
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php"><i class="bi bi-shield-lock"></i> Change Password</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="bi bi-gear"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container dashboard-container">
        <?php if (isset($_GET['password_changed'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> Your password has been changed successfully! Welcome to your dashboard.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- Welcome Section -->
        <div class="welcome-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2>Welcome back, <?php echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']); ?>!</h2>
                    <p>Here's what's happening in your church community today.</p>
                </div>
                <div class="col-md-4 text-end">
                    <?php if (!empty($userData['image_path'])): ?>
                        <img src="../<?php echo htmlspecialchars($userData['image_path']); ?>" alt="Profile" class="profile-avatar">
                    <?php else: ?>
                        <div class="profile-avatar d-inline-flex align-items-center justify-content-center" style="background-color: rgba(255,255,255,0.2);">
                            <i class="bi bi-person-fill" style="font-size: 2rem;"></i>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Birthday Section -->
        <?php if (!empty($todaysBirthdays) || !empty($upcomingBirthdays)): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <h5><i class="bi bi-gift"></i> Birthday Celebrations</h5>

                    <?php if (!empty($todaysBirthdays)): ?>
                    <div class="birthday-today mb-4">
                        <h6 class="text-primary"><i class="bi bi-calendar-heart"></i> Today's Birthdays</h6>
                        <div class="row">
                            <?php foreach ($todaysBirthdays as $birthday): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="birthday-card p-3 border rounded-3 bg-light">
                                    <div class="d-flex align-items-center">
                                        <div class="birthday-avatar me-3">
                                            <?php if (!empty($birthday['image_path'])): ?>
                                                <img src="../<?php echo htmlspecialchars($birthday['image_path']); ?>"
                                                     alt="<?php echo htmlspecialchars($birthday['full_name']); ?>"
                                                     class="rounded-circle" style="width: 50px; height: 50px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px;">
                                                    <i class="bi bi-person-fill text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($birthday['full_name']); ?></h6>
                                            <small class="text-muted">🎂 Today!</small>
                                        </div>
                                        <div>
                                            <a href="send_birthday_message.php?member_id=<?php echo $birthday['id']; ?>"
                                               class="btn btn-sm btn-primary">
                                                <i class="bi bi-envelope-heart"></i> Send Wishes
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($upcomingBirthdays)): ?>
                    <div class="birthday-upcoming">
                        <h6 class="text-info"><i class="bi bi-calendar-week"></i> Upcoming Birthdays</h6>
                        <div class="list-group list-group-flush">
                            <?php foreach ($upcomingBirthdays as $birthday): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="birthday-avatar me-3">
                                        <?php if (!empty($birthday['image_path'])): ?>
                                            <img src="../<?php echo htmlspecialchars($birthday['image_path']); ?>"
                                                 alt="<?php echo htmlspecialchars($birthday['full_name']); ?>"
                                                 class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                                                 style="width: 40px; height: 40px;">
                                                <i class="bi bi-person-fill text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($birthday['full_name']); ?></h6>
                                        <small class="text-muted">
                                            <?php
                                            $birthDate = new DateTime($birthday['birth_date']);
                                            echo $birthDate->format('M j');
                                            ?>
                                        </small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-light text-dark me-2">
                                        <?php echo $birthday['days_until']; ?> day<?php echo $birthday['days_until'] != 1 ? 's' : ''; ?>
                                    </span>
                                    <a href="send_birthday_message.php?member_id=<?php echo $birthday['id']; ?>"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-envelope"></i>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="text-center mt-3">
                        <a href="birthday_templates.php" class="btn btn-outline-primary">
                            <i class="bi bi-collection"></i> Browse Birthday Templates
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="row">
            <!-- Quick Actions -->
            <div class="col-md-8">
                <div class="dashboard-card">
                    <h5><i class="bi bi-lightning"></i> Quick Actions</h5>
                    <div class="d-flex flex-wrap">
                        <a href="profile.php" class="quick-action-btn">
                            <i class="bi bi-person-gear"></i> Edit Profile
                        </a>
                        <a href="events.php" class="quick-action-btn">
                            <i class="bi bi-calendar-plus"></i> View Events
                        </a>
                        <a href="settings.php" class="quick-action-btn">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                        <a href="change_password.php" class="quick-action-btn">
                            <i class="bi bi-shield-lock"></i> Change Password
                        </a>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-card">
                    <h5><i class="bi bi-activity"></i> Recent Activity</h5>
                    <?php if (empty($recentActivity) && empty($recentBirthdayMessages)): ?>
                        <p class="text-muted">No recent activity to display.</p>
                        <small class="text-muted">Your activities will appear here as you interact with the system.</small>
                    <?php else: ?>
                        <div class="activity-list">
                            <?php foreach ($recentBirthdayMessages as $message): ?>
                            <div class="activity-item d-flex align-items-center mb-3 p-2 bg-light rounded">
                                <div class="activity-icon me-3">
                                    <i class="bi bi-envelope-heart text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="activity-description">
                                        <strong>Birthday message sent</strong> to <?php echo htmlspecialchars($message['recipient_name']); ?>
                                        <?php if ($message['template_name']): ?>
                                            <small class="text-muted">using "<?php echo htmlspecialchars($message['template_name']); ?>" template</small>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('M j, Y g:i A', strtotime($message['created_at'])); ?>
                                    </small>
                                </div>
                                <div class="activity-status">
                                    <?php if ($message['status'] == 'sent'): ?>
                                        <span class="badge bg-success">Sent</span>
                                    <?php elseif ($message['status'] == 'scheduled'): ?>
                                        <span class="badge bg-warning">Scheduled</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary"><?php echo ucfirst($message['status']); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>

                            <?php foreach ($recentActivity as $activity): ?>
                            <div class="activity-item d-flex align-items-center mb-3 p-2 bg-light rounded">
                                <div class="activity-icon me-3">
                                    <?php
                                    $icon = 'bi-activity';
                                    switch($activity['activity_type']) {
                                        case 'login': $icon = 'bi-box-arrow-in-right text-success'; break;
                                        case 'logout': $icon = 'bi-box-arrow-right text-warning'; break;
                                        case 'profile_update': $icon = 'bi-person-gear text-info'; break;
                                        case 'password_change': $icon = 'bi-shield-lock text-primary'; break;
                                        default: $icon = 'bi-activity text-secondary';
                                    }
                                    ?>
                                    <i class="bi <?php echo $icon; ?>"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="activity-description">
                                        <?php echo htmlspecialchars($activity['activity_description']); ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('M j, Y g:i A', strtotime($activity['created_at'])); ?>
                                    </small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Stats & Info -->
            <div class="col-md-4">
                <div class="dashboard-card stat-card">
                    <div class="stat-number"><?php echo count($todaysBirthdays); ?></div>
                    <div class="stat-label">Today's Birthdays</div>
                </div>

                <div class="dashboard-card stat-card">
                    <div class="stat-number"><?php echo count($recentBirthdayMessages); ?></div>
                    <div class="stat-label">Birthday Messages Sent</div>
                </div>

                <div class="dashboard-card">
                    <h5><i class="bi bi-info-circle"></i> Account Info</h5>
                    <p><strong>Email:</strong> <?php echo htmlspecialchars($userData['email']); ?></p>
                    <?php if (!empty($userData['phone_number'])): ?>
                        <p><strong>Phone:</strong> <?php echo htmlspecialchars($userData['phone_number']); ?></p>
                    <?php endif; ?>
                    <p><strong>Member since:</strong> <?php echo date('M Y', strtotime($userData['created_at'])); ?></p>
                    <?php if (!empty($userData['last_login_at'])): ?>
                        <p><strong>Last login:</strong> <?php echo date('M j, Y g:i A', strtotime($userData['last_login_at'])); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
