<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

$message = '';
$error = '';

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    echo "<h3>PHP Upload Configuration:</h3>";
    echo "<ul>";
    echo "<li>upload_max_filesize: " . ini_get('upload_max_filesize') . "</li>";
    echo "<li>post_max_size: " . ini_get('post_max_size') . "</li>";
    echo "<li>max_file_uploads: " . ini_get('max_file_uploads') . "</li>";
    echo "<li>memory_limit: " . ini_get('memory_limit') . "</li>";
    echo "<li>max_execution_time: " . ini_get('max_execution_time') . "</li>";
    echo "</ul>";
    
    echo "<h3>Upload Directory Information:</h3>";
    $upload_dir = '../uploads/profiles/';
    echo "<ul>";
    echo "<li>Upload directory: " . realpath($upload_dir) . "</li>";
    echo "<li>Directory exists: " . (is_dir($upload_dir) ? 'Yes' : 'No') . "</li>";
    echo "<li>Directory writable: " . (is_writable($upload_dir) ? 'Yes' : 'No') . "</li>";
    echo "<li>Directory permissions: " . substr(sprintf('%o', fileperms($upload_dir)), -4) . "</li>";
    echo "</ul>";
    
    echo "<h3>File Upload Information:</h3>";
    if (isset($_FILES['test_image'])) {
        echo "<pre>";
        print_r($_FILES['test_image']);
        echo "</pre>";
        
        $file = $_FILES['test_image'];
        
        echo "<h4>Upload Error Analysis:</h4>";
        switch($file['error']) {
            case UPLOAD_ERR_OK:
                echo "No error, file uploaded successfully.";
                break;
            case UPLOAD_ERR_INI_SIZE:
                echo "The uploaded file exceeds the upload_max_filesize directive in php.ini.";
                break;
            case UPLOAD_ERR_FORM_SIZE:
                echo "The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.";
                break;
            case UPLOAD_ERR_PARTIAL:
                echo "The uploaded file was only partially uploaded.";
                break;
            case UPLOAD_ERR_NO_FILE:
                echo "No file was uploaded.";
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                echo "Missing a temporary folder.";
                break;
            case UPLOAD_ERR_CANT_WRITE:
                echo "Failed to write file to disk.";
                break;
            case UPLOAD_ERR_EXTENSION:
                echo "A PHP extension stopped the file upload.";
                break;
            default:
                echo "Unknown upload error.";
                break;
        }
        
        if ($file['error'] == UPLOAD_ERR_OK) {
            echo "<h4>Attempting File Upload:</h4>";
            
            $allowed = ['jpg', 'jpeg', 'png', 'gif'];
            $filename = $file['name'];
            $file_ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            
            if (in_array($file_ext, $allowed)) {
                // Create unique filename
                $new_filename = uniqid() . '.' . $file_ext;
                $upload_path = $upload_dir . $new_filename;
                
                echo "<ul>";
                echo "<li>Original filename: " . htmlspecialchars($filename) . "</li>";
                echo "<li>New filename: " . htmlspecialchars($new_filename) . "</li>";
                echo "<li>Upload path: " . htmlspecialchars($upload_path) . "</li>";
                echo "<li>Temp file: " . htmlspecialchars($file['tmp_name']) . "</li>";
                echo "<li>Temp file exists: " . (file_exists($file['tmp_name']) ? 'Yes' : 'No') . "</li>";
                echo "</ul>";
                
                if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                    $message = "File uploaded successfully! Path: uploads/profiles/" . $new_filename;
                    echo "<div style='color: green; font-weight: bold;'>SUCCESS: " . $message . "</div>";
                } else {
                    $error = "Failed to move uploaded file.";
                    echo "<div style='color: red; font-weight: bold;'>ERROR: " . $error . "</div>";
                }
            } else {
                $error = "Invalid file type. Only JPG, JPEG, PNG, and GIF files are allowed.";
                echo "<div style='color: red; font-weight: bold;'>ERROR: " . $error . "</div>";
            }
        }
    } else {
        echo "No file uploaded.";
    }
}

// Set page variables
$page_title = 'Test File Upload';
$page_header = 'Test File Upload';
$page_description = 'Test file upload functionality.';

// Include header
include 'includes/header.php';
?>

<div class="card">
    <div class="card-body">
        <h5 class="card-title mb-4">File Upload Test</h5>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" enctype="multipart/form-data">
            <div class="mb-3">
                <label for="test_image" class="form-label">Test Image Upload</label>
                <input type="file" class="form-control" id="test_image" name="test_image" accept="image/*">
                <small class="form-text text-muted">Upload a test image to check file upload functionality.</small>
            </div>
            
            <button type="submit" class="btn btn-primary">Test Upload</button>
            <a href="add_member.php" class="btn btn-secondary">Back to Add Member</a>
        </form>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
