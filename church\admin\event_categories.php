<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_category':
                try {
                    $stmt = $conn->prepare("INSERT INTO event_categories (name, description, color_code) VALUES (?, ?, ?)");
                    $stmt->execute([
                        $_POST['name'],
                        $_POST['description'],
                        $_POST['color_code']
                    ]);
                    $message = "Category created successfully!";
                } catch (PDOException $e) {
                    $error = "Error creating category: " . $e->getMessage();
                }
                break;
                
            case 'update_category':
                try {
                    $stmt = $conn->prepare("UPDATE event_categories SET name = ?, description = ?, color_code = ? WHERE id = ?");
                    $stmt->execute([
                        $_POST['name'],
                        $_POST['description'],
                        $_POST['color_code'],
                        (int)$_POST['category_id']
                    ]);
                    $message = "Category updated successfully!";
                } catch (PDOException $e) {
                    $error = "Error updating category: " . $e->getMessage();
                }
                break;
                
            case 'delete_category':
                try {
                    // Check if category is being used
                    $check_stmt = $conn->prepare("SELECT COUNT(*) FROM events WHERE category_id = ?");
                    $check_stmt->execute([(int)$_POST['category_id']]);
                    $event_count = $check_stmt->fetchColumn();
                    
                    if ($event_count > 0) {
                        $error = "Cannot delete category. It is being used by $event_count event(s).";
                    } else {
                        $stmt = $conn->prepare("DELETE FROM event_categories WHERE id = ?");
                        $stmt->execute([(int)$_POST['category_id']]);
                        $message = "Category deleted successfully!";
                    }
                } catch (PDOException $e) {
                    $error = "Error deleting category: " . $e->getMessage();
                }
                break;
        }
    }
}

// Get all categories with event counts
$stmt = $conn->query("
    SELECT ec.*,
           0 as event_count
    FROM event_categories ec
    GROUP BY ec.id
    ORDER BY ec.name
");
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Page title and header info
$page_title = "Event Categories";
$page_header = "Event Categories";
$page_description = "Manage categories for organizing church events.";

// Include header
include 'includes/header.php';
?>

<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
            <i class="bi bi-plus-circle"></i> Create Category
        </button>
    </div>
    <div>
        <a href="events.php" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Events
        </a>
    </div>
</div>

<!-- Categories Grid -->
<div class="row">
    <?php if (empty($categories)): ?>
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-tags display-1 text-muted"></i>
                <h4 class="mt-3">No Categories Found</h4>
                <p class="text-muted">Create your first event category to get started.</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
                    <i class="bi bi-plus-circle"></i> Create Category
                </button>
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($categories as $category): ?>
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center" 
                         style="background-color: <?= htmlspecialchars($category['color_code']) ?>; color: white;">
                        <h6 class="mb-0"><?= htmlspecialchars($category['name']) ?></h6>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots-vertical"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <button class="dropdown-item edit-category-btn" 
                                            data-category='<?= json_encode($category) ?>'>
                                        <i class="bi bi-pencil"></i> Edit
                                    </button>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <button class="dropdown-item text-danger delete-category-btn"
                                            data-id="<?= $category['id'] ?>" 
                                            data-name="<?= htmlspecialchars($category['name']) ?>"
                                            data-count="<?= $category['event_count'] ?>">
                                        <i class="bi bi-trash"></i> Delete
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($category['description']): ?>
                            <p class="card-text"><?= htmlspecialchars($category['description']) ?></p>
                        <?php else: ?>
                            <p class="card-text text-muted">No description provided.</p>
                        <?php endif; ?>
                        
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <small class="text-muted">
                                <?= $category['event_count'] ?> event<?= $category['event_count'] != 1 ? 's' : '' ?>
                            </small>
                            <div class="color-preview" 
                                 style="width: 20px; height: 20px; background-color: <?= htmlspecialchars($category['color_code']) ?>; border-radius: 3px; border: 1px solid #dee2e6;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalLabel">Create Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="categoryForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" id="categoryAction" value="create_category">
                    <input type="hidden" name="category_id" id="categoryId">
                    
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Category Name *</label>
                        <input type="text" class="form-control" id="categoryName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="categoryDescription" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="categoryColor" class="form-label">Color</label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color me-2" 
                                   id="categoryColor" name="color_code" value="#6f42c1">
                            <small class="text-muted">Choose a color to represent this category</small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <span id="submitButtonText">Create Category</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the category "<span id="deleteCategoryName"></span>"?</p>
                <div id="deleteWarning" class="alert alert-warning" style="display: none;">
                    <strong>Warning:</strong> This category is being used by <span id="deleteEventCount"></span> event(s). 
                    You cannot delete it until all events are moved to other categories or deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete_category">
                    <input type="hidden" name="category_id" id="deleteCategoryId">
                    <button type="submit" class="btn btn-danger" id="deleteConfirmBtn">Delete Category</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Edit category functionality
    document.querySelectorAll('.edit-category-btn').forEach(button => {
        button.addEventListener('click', function() {
            const categoryData = JSON.parse(this.getAttribute('data-category'));

            // Update modal title and form action
            document.getElementById('categoryModalLabel').textContent = 'Edit Category';
            document.getElementById('categoryAction').value = 'update_category';
            document.getElementById('categoryId').value = categoryData.id;
            document.getElementById('submitButtonText').textContent = 'Update Category';

            // Populate form fields
            document.getElementById('categoryName').value = categoryData.name || '';
            document.getElementById('categoryDescription').value = categoryData.description || '';
            document.getElementById('categoryColor').value = categoryData.color_code || '#6f42c1';

            // Show modal
            new bootstrap.Modal(document.getElementById('categoryModal')).show();
        });
    });

    // Reset modal when creating new category
    document.querySelector('[data-bs-target="#categoryModal"]').addEventListener('click', function() {
        // Reset modal title and form action
        document.getElementById('categoryModalLabel').textContent = 'Create Category';
        document.getElementById('categoryAction').value = 'create_category';
        document.getElementById('categoryId').value = '';
        document.getElementById('submitButtonText').textContent = 'Create Category';

        // Reset form
        document.getElementById('categoryForm').reset();
        document.getElementById('categoryColor').value = '#6f42c1';
    });

    // Delete category functionality
    document.querySelectorAll('.delete-category-btn').forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.getAttribute('data-id');
            const categoryName = this.getAttribute('data-name');
            const eventCount = parseInt(this.getAttribute('data-count'));

            document.getElementById('deleteCategoryId').value = categoryId;
            document.getElementById('deleteCategoryName').textContent = categoryName;

            const warningDiv = document.getElementById('deleteWarning');
            const deleteBtn = document.getElementById('deleteConfirmBtn');

            if (eventCount > 0) {
                document.getElementById('deleteEventCount').textContent = eventCount;
                warningDiv.style.display = 'block';
                deleteBtn.disabled = true;
                deleteBtn.textContent = 'Cannot Delete';
            } else {
                warningDiv.style.display = 'none';
                deleteBtn.disabled = false;
                deleteBtn.textContent = 'Delete Category';
            }

            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
