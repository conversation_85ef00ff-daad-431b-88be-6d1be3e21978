<?php
session_start();
require_once __DIR__ . '/config.php';

echo "<h2>RSVP Debug</h2>";

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>POST Data Received:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    // Simulate the RSVP handler logic
    $eventId = filter_input(INPUT_POST, 'event_id', FILTER_VALIDATE_INT);
    $status = filter_input(INPUT_POST, 'status', FILTER_SANITIZE_STRING);
    $notes = filter_input(INPUT_POST, 'notes', FILTER_SANITIZE_STRING);
    
    echo "<h3>Processed Data:</h3>";
    echo "Event ID: " . ($eventId ?: 'INVALID') . "<br>";
    echo "Status: " . ($status ?: 'EMPTY') . "<br>";
    echo "Notes: " . ($notes ?: 'EMPTY') . "<br>";
    
    if (!isset($_SESSION['user_id'])) {
        echo "<p style='color: red;'>❌ User not logged in!</p>";
        exit();
    }
    
    $userId = $_SESSION['user_id'];
    echo "User ID: $userId<br>";
    
    if ($eventId && $status) {
        try {
            // Check if RSVP exists
            $stmt = $pdo->prepare("SELECT id FROM event_rsvps WHERE event_id = ? AND user_id = ?");
            $stmt->execute([$eventId, $userId]);
            $existingRsvp = $stmt->fetch();
            
            if ($existingRsvp) {
                // Update existing RSVP
                $stmt = $pdo->prepare("
                    UPDATE event_rsvps 
                    SET status = ?, notes = ?, updated_at = NOW()
                    WHERE event_id = ? AND user_id = ?
                ");
                $stmt->execute([$status, $notes, $eventId, $userId]);
                echo "<p style='color: green;'>✓ RSVP updated successfully!</p>";
            } else {
                // Create new RSVP
                $stmt = $pdo->prepare("
                    INSERT INTO event_rsvps (event_id, user_id, status, notes, created_at, updated_at)
                    VALUES (?, ?, ?, ?, NOW(), NOW())
                ");
                $stmt->execute([$eventId, $userId, $status, $notes]);
                echo "<p style='color: green;'>✓ RSVP created successfully!</p>";
            }
            
            echo json_encode(['success' => true, 'message' => 'RSVP processed successfully']);
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
            echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
        }
    } else {
        echo "<p style='color: red;'>❌ Missing required fields</p>";
        echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    }
    exit();
}
?>

<h3>Test RSVP Submission</h3>
<form method="POST" action="">
    <input type="hidden" name="event_id" value="3">
    <input type="hidden" name="action" value="rsvp">
    
    <div>
        <label>Status:</label><br>
        <input type="radio" name="status" value="attending" id="attending">
        <label for="attending">Attending</label><br>
        
        <input type="radio" name="status" value="maybe" id="maybe" checked>
        <label for="maybe">Maybe</label><br>
        
        <input type="radio" name="status" value="not_attending" id="not_attending">
        <label for="not_attending">Not Attending</label><br>
    </div>
    
    <div style="margin-top: 10px;">
        <label for="notes">Notes:</label><br>
        <textarea name="notes" id="notes" rows="3" cols="50">Test update from debug script</textarea>
    </div>
    
    <div style="margin-top: 10px;">
        <button type="submit" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;">
            Submit RSVP
        </button>
    </div>
</form>

<p><a href="user/events.php">Back to Events</a></p>
