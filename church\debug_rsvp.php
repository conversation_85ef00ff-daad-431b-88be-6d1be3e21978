<?php
session_start();
require_once __DIR__ . '/config.php';

echo "<h2>RSVP Debug</h2>";

// Check session and user
echo "<h3>Session Info:</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "User logged in: " . (isset($_SESSION['user_id']) ? 'Yes' : 'No') . "<br>";
if (isset($_SESSION['user_id'])) {
    echo "User ID: " . $_SESSION['user_id'] . "<br>";
}

// Check events
echo "<h3>Available Events:</h3>";
try {
    $stmt = $pdo->prepare("SELECT id, title, event_date, event_time FROM events WHERE is_active = 1 ORDER BY event_date");
    $stmt->execute();
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if ($events) {
        echo "<ul>";
        foreach ($events as $event) {
            echo "<li>ID: {$event['id']} - {$event['title']} - {$event['event_date']} {$event['event_time']}</li>";
        }
        echo "</ul>";
    } else {
        echo "No active events found.<br>";
    }
} catch (PDOException $e) {
    echo "Error fetching events: " . $e->getMessage() . "<br>";
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>POST Data Received:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";

    // Test the actual RSVP handler
    echo "<h3>Testing RSVP Handler:</h3>";

    // Prepare data for RSVP handler
    $postData = http_build_query($_POST);

    // Use cURL to test the RSVP handler
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/campaign/church/user/rsvp_handler.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
    curl_setopt($ch, CURLOPT_HEADER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Code: $httpCode<br>";
    echo "Response:<br>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";

    exit();
}
?>

<h3>Test RSVP Submission</h3>
<form method="POST" action="">
    <input type="hidden" name="event_id" value="3">
    <input type="hidden" name="action" value="rsvp">
    
    <div>
        <label>Status:</label><br>
        <input type="radio" name="status" value="attending" id="attending">
        <label for="attending">Attending</label><br>
        
        <input type="radio" name="status" value="maybe" id="maybe" checked>
        <label for="maybe">Maybe</label><br>
        
        <input type="radio" name="status" value="not_attending" id="not_attending">
        <label for="not_attending">Not Attending</label><br>
    </div>
    
    <div style="margin-top: 10px;">
        <label for="notes">Notes:</label><br>
        <textarea name="notes" id="notes" rows="3" cols="50">Test update from debug script</textarea>
    </div>
    
    <div style="margin-top: 10px;">
        <button type="submit" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;">
            Submit RSVP
        </button>
    </div>
</form>

<p><a href="user/events.php">Back to Events</a></p>
