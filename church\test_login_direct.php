<?php
/**
 * Direct login test to bypass form issues
 */

session_start();

// Include configuration
require_once 'config.php';
require_once 'classes/SecurityManager.php';
require_once 'classes/UserAuthManager.php';

echo "<h1>Direct Login Test</h1>";

try {
    $security = new SecurityManager($pdo);
    $userAuth = new UserAuthManager($pdo, $security);
    
    // Test direct authentication
    $identifier = '<EMAIL>';
    $password = 'TestFixed123!';
    
    echo "<h2>Testing Direct Authentication</h2>";
    echo "Email: " . htmlspecialchars($identifier) . "<br>";
    echo "Password: " . htmlspecialchars($password) . "<br><br>";
    
    $result = $userAuth->authenticateUser($identifier, $password);
    
    echo "<h3>Authentication Result:</h3>";
    echo "Success: " . ($result['success'] ? '✅ YES' : '❌ NO') . "<br>";
    echo "Message: " . htmlspecialchars($result['message']) . "<br>";
    
    if ($result['success']) {
        echo "<h3>Session Variables Set:</h3>";
        echo "user_id: " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'NOT SET') . "<br>";
        echo "user_name: " . (isset($_SESSION['user_name']) ? htmlspecialchars($_SESSION['user_name']) : 'NOT SET') . "<br>";
        echo "user_email: " . (isset($_SESSION['user_email']) ? htmlspecialchars($_SESSION['user_email']) : 'NOT SET') . "<br>";
        echo "last_activity: " . (isset($_SESSION['last_activity']) ? date('Y-m-d H:i:s', $_SESSION['last_activity']) : 'NOT SET') . "<br>";
        
        echo "<h3>Test Dashboard Access:</h3>";
        echo "<a href='user/dashboard.php' target='_blank'>Try Dashboard Access</a><br>";
        
        echo "<h3>Authentication Status:</h3>";
        echo "Is Authenticated: " . ($userAuth->isAuthenticated() ? '✅ YES' : '❌ NO') . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . htmlspecialchars($e->getMessage()) . "<br>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
