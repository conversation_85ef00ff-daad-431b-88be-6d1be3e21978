<?php
session_start();
require_once __DIR__ . '/config.php';

echo "<h2>RSVP Test</h2>";

// Check session
echo "<h3>Session Information:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ User is not logged in!</p>";
    echo "<p><a href='user/login.php'>Login here</a></p>";
    exit();
}

$userId = $_SESSION['user_id'];
echo "<p style='color: green;'>✓ User is logged in with ID: $userId</p>";

// Test database connection
try {
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "<p style='color: green;'>✓ User found in database: " . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ User not found in database!</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test events
echo "<h3>Available Events:</h3>";
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE is_active = 1 ORDER BY event_date ASC LIMIT 5");
    $stmt->execute();
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($events) {
        echo "<ul>";
        foreach ($events as $event) {
            echo "<li>ID: {$event['id']} - {$event['title']} - {$event['event_date']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No events found.</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error fetching events: " . $e->getMessage() . "</p>";
}

// Test RSVP table structure
echo "<h3>RSVP Table Structure:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE event_rsvps");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>{$column['Field']} - {$column['Type']}</li>";
    }
    echo "</ul>";
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error checking RSVP table: " . $e->getMessage() . "</p>";
}

// Test RSVP insertion
if (isset($_POST['test_rsvp'])) {
    echo "<h3>Testing RSVP Insertion:</h3>";
    $eventId = 3; // Summer Fellowship Gathering
    $status = 'attending';
    $notes = 'Test RSVP';
    
    try {
        // Check if RSVP already exists
        $stmt = $pdo->prepare("SELECT id FROM event_rsvps WHERE event_id = ? AND user_id = ?");
        $stmt->execute([$eventId, $userId]);
        $existingRsvp = $stmt->fetch();
        
        if ($existingRsvp) {
            // Update existing RSVP
            $stmt = $pdo->prepare("
                UPDATE event_rsvps 
                SET status = ?, notes = ?, updated_at = NOW()
                WHERE event_id = ? AND user_id = ?
            ");
            $stmt->execute([$status, $notes, $eventId, $userId]);
            echo "<p style='color: green;'>✓ RSVP updated successfully!</p>";
        } else {
            // Create new RSVP
            $stmt = $pdo->prepare("
                INSERT INTO event_rsvps (event_id, user_id, status, notes, created_at, updated_at)
                VALUES (?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([$eventId, $userId, $status, $notes]);
            echo "<p style='color: green;'>✓ RSVP created successfully!</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ RSVP error: " . $e->getMessage() . "</p>";
    }
}
?>

<form method="POST">
    <button type="submit" name="test_rsvp" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;">
        Test RSVP Insertion
    </button>
</form>

<p><a href="user/events.php">Back to Events</a></p>
