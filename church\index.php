<?php
// Start session for any session-based functionality
session_start();

// Include configuration
require_once 'config.php';

// Get URL parameters
$success = isset($_GET['success']) ? $_GET['success'] : null;
$email_sent = isset($_GET['email']) ? $_GET['email'] : null;
$error = isset($_GET['error']) ? $_GET['error'] : null;

// Get site settings for branding
$sitename = 'Church Management System';
try {
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
    $stmt->execute();
    $siteNameResult = $stmt->fetch();
    if ($siteNameResult) {
        $sitename = $siteNameResult['setting_value'];
    }
} catch (Exception $e) {
    // Use default if settings table doesn't exist
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($sitename); ?> - Welcome</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <?php include 'includes/frontend_css.php'; ?>
    
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        .success-alert {
            border-left: 5px solid #28a745;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error-alert {
            border-left: 5px solid #dc3545;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .btn-church {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .btn-church:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(45deg, #667eea, #764ba2);">
        <div class="container">
            <a class="navbar-brand" href="#"><i class="fas fa-church"></i> <?php echo htmlspecialchars($sitename); ?></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="register.php"><i class="fas fa-user-plus"></i> Register</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="user/login.php"><i class="fas fa-sign-in-alt"></i> Member Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin/login.php"><i class="fas fa-cog"></i> Admin Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../"><i class="fas fa-home"></i> Main Site</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="display-4 mb-4">Welcome to Our Church Family</h1>
            <p class="lead mb-4">Join our community of faith, fellowship, and service</p>
            
            <?php if ($success): ?>
                <div class="alert success-alert mx-auto" style="max-width: 600px;">
                    <h4 class="alert-heading text-success"><i class="fas fa-check-circle"></i> Registration Successful!</h4>
                    <p class="mb-0 text-dark">Thank you for joining our church family! Your registration has been completed successfully.</p>
                    <?php if ($email_sent == '1'): ?>
                        <p class="mb-0 text-dark mt-2"><i class="fas fa-envelope"></i> A welcome email has been sent to your email address.</p>
                    <?php elseif ($email_sent == '0'): ?>
                        <p class="mb-0 text-dark mt-2"><i class="fas fa-exclamation-triangle"></i> Registration successful, but we couldn't send the welcome email. Please contact us if you need assistance.</p>
                    <?php endif; ?>
                    <hr class="my-3">
                    <p class="mb-0 text-dark">
                        <a href="user/login.php" class="btn btn-success me-2"><i class="fas fa-sign-in-alt"></i> Login Now</a>
                        <a href="register.php" class="btn btn-outline-success">Register Another Member</a>
                    </p>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert error-alert mx-auto" style="max-width: 600px;">
                    <h4 class="alert-heading text-danger"><i class="fas fa-exclamation-circle"></i> Registration Error</h4>
                    <p class="mb-0 text-dark">
                        <?php
                        switch ($error) {
                            case 'duplicate_email':
                                echo 'This email address is already registered. Please use a different email or try logging in.';
                                break;
                            case 'db_error':
                                echo 'There was a database error. Please try again later or contact support.';
                                break;
                            case 'general':
                                echo 'There was an error processing your registration. Please try again.';
                                break;
                            default:
                                echo 'An unexpected error occurred. Please try again.';
                        }
                        ?>
                    </p>
                    <hr class="my-3">
                    <p class="mb-0 text-dark">
                        <a href="register.php" class="btn btn-danger me-2"><i class="fas fa-redo"></i> Try Again</a>
                        <a href="user/login.php" class="btn btn-outline-danger">Login Instead</a>
                    </p>
                </div>
            <?php endif; ?>

            <?php if (!$success && !$error): ?>
                <a href="register.php" class="btn btn-church btn-lg me-3"><i class="fas fa-user-plus"></i> Join Our Family</a>
                <a href="user/login.php" class="btn btn-outline-light btn-lg"><i class="fas fa-sign-in-alt"></i> Member Login</a>
            <?php endif; ?>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="display-5 mb-3">What We Offer</h2>
                    <p class="lead text-muted">Experience the joy of being part of our church community</p>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Community Fellowship</h5>
                            <p class="card-text">Join a warm and welcoming community where lasting friendships are formed and faith is strengthened together.</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-pray fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Spiritual Growth</h5>
                            <p class="card-text">Deepen your relationship with God through worship, prayer, and Bible study in a supportive environment.</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-hands-helping fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Service Opportunities</h5>
                            <p class="card-text">Make a difference in your community through various outreach programs and volunteer opportunities.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-5 bg-light">
        <div class="container text-center">
            <h3 class="mb-4">Ready to Join Our Church Family?</h3>
            <p class="lead mb-4">Take the first step in your spiritual journey with us</p>
            <a href="register.php" class="btn btn-church btn-lg me-3"><i class="fas fa-user-plus"></i> Register Now</a>
            <a href="user/login.php" class="btn btn-outline-primary btn-lg"><i class="fas fa-sign-in-alt"></i> Already a Member?</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4">
        <div class="container text-center">
            <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($sitename); ?>. All rights reserved.</p>
            <p class="mb-0 mt-2">
                <a href="../" class="text-light me-3"><i class="fas fa-home"></i> Main Website</a>
                <a href="admin/login.php" class="text-light"><i class="fas fa-cog"></i> Admin Portal</a>
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
